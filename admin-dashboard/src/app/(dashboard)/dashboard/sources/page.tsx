'use client';

import { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Play,
  Pause,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Zap,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { sourceService } from '@/lib/api/services';
import { DataSource, ProcessingStatus, SourceType } from '@/lib/types';
import { formatDate } from '@/lib/utils';
import { toast } from 'sonner';
import { SourceForm } from '@/components/forms/SourceForm';
import { usePipelineStore } from '@/lib/stores/pipeline';
import { EmptyState } from '@/components/ui/empty-state';
import { SourceStatusBadge } from '@/components/ui/status-badge';
import { LoadingOverlay } from '@/components/ui/loading-overlay';

export default function SourcesPage() {
  const [sources, setSources] = useState<DataSource[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingSource, setEditingSource] = useState<DataSource | null>(null);

  // Use shared pipeline store for source selection
  const { selectedSources, toggleSourceSelection, clearSelectedSources } = usePipelineStore();

  const loadSources = async () => {
    try {
      setLoading(true);
      const data = await sourceService.listSources();
      setSources(data);
    } catch (error: any) {
      toast.error('Failed to load sources: ' + error.message);
      // Mock data for development
      setSources([
        {
          id: '1',
          name: 'Legal Documents Collection',
          source_type: SourceType.WEBSITE,
          url: 'https://legal-docs.example.com',
          enabled: true,
          status: ProcessingStatus.COMPLETED,
          last_processed: '2024-01-15T10:30:00Z',
          metadata: { documents: 150, size: '2.5MB' }
        },
        {
          id: '2',
          name: 'Contract Templates',
          source_type: SourceType.PDF,
          path: '/data/contracts/',
          enabled: true,
          status: ProcessingStatus.PROCESSING,
          last_processed: '2024-01-15T09:15:00Z',
          metadata: { documents: 75, size: '1.8MB' }
        },
        {
          id: '3',
          name: 'Case Law Database',
          source_type: SourceType.WEBSITE,
          url: 'https://caselaw.example.com',
          enabled: false,
          status: ProcessingStatus.FAILED,
          last_processed: '2024-01-14T16:45:00Z',
          metadata: { documents: 0, size: '0MB' }
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSources();
  }, []);

  const filteredSources = sources.filter(source =>
    source.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    source.source_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectSource = (sourceId: string, checked: boolean) => {
    toggleSourceSelection(sourceId);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Select all filtered sources
      const pipelineStore = usePipelineStore.getState();
      filteredSources.forEach(source => {
        if (!selectedSources.includes(source.id!)) {
          pipelineStore.addSelectedSource(source.id!);
        }
      });
    } else {
      // Clear all selections
      clearSelectedSources();
    }
  };

  const handleDeleteSource = async (sourceId: string) => {
    try {
      await sourceService.deleteSource(sourceId);
      setSources(sources.filter(s => s.id !== sourceId));
      toast.success('Source deleted successfully');
    } catch (error: any) {
      toast.error('Failed to delete source: ' + error.message);
    }
  };

  const handleToggleSource = async (sourceId: string, enabled: boolean) => {
    try {
      const source = sources.find(s => s.id === sourceId);
      if (!source) return;

      const updatedSource = await sourceService.updateSource(sourceId, {
        ...source,
        enabled
      });
      
      setSources(sources.map(s => s.id === sourceId ? updatedSource : s));
      toast.success(`Source ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error: any) {
      toast.error('Failed to update source: ' + error.message);
    }
  };

  const handleBulkAction = async (action: 'enable' | 'disable' | 'delete') => {
    if (selectedSources.length === 0) {
      toast.error('No sources selected');
      return;
    }

    try {
      switch (action) {
        case 'enable':
          await sourceService.bulkEnableSources(selectedSources);
          toast.success(`${selectedSources.length} sources enabled`);
          break;
        case 'disable':
          await sourceService.bulkDisableSources(selectedSources);
          toast.success(`${selectedSources.length} sources disabled`);
          break;
        case 'delete':
          await Promise.all(selectedSources.map(id => sourceService.deleteSource(id)));
          setSources(sources.filter(s => !selectedSources.includes(s.id!)));
          toast.success(`${selectedSources.length} sources deleted`);
          break;
      }
      setSelectedSources([]);
      if (action !== 'delete') {
        loadSources();
      }
    } catch (error: any) {
      toast.error('Bulk operation failed: ' + error.message);
    }
  };

  const getStatusBadge = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.COMPLETED:
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case ProcessingStatus.PROCESSING:
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="w-3 h-3 mr-1" />Processing</Badge>;
      case ProcessingStatus.FAILED:
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      case ProcessingStatus.PENDING:
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertCircle className="w-3 h-3 mr-1" />Pending</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getSourceTypeIcon = (type: SourceType) => {
    switch (type) {
      case SourceType.WEBSITE:
        return '🌐';
      case SourceType.PDF:
        return '📄';
      case SourceType.TEXT:
        return '📝';
      default:
        return '📁';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Sources</h1>
          <p className="text-gray-600 mt-1">
            Manage your data sources and monitor processing status
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={loadSources} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Source
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Data Source</DialogTitle>
                <DialogDescription>
                  Configure a new data source for the RAG system
                </DialogDescription>
              </DialogHeader>
              <SourceForm
                source={editingSource || undefined}
                onSuccess={(source) => {
                  if (editingSource) {
                    setSources(sources.map(s => s.id === source.id ? source : s));
                  } else {
                    setSources([...sources, source]);
                  }
                  setShowAddDialog(false);
                  setEditingSource(null);
                }}
                onCancel={() => {
                  setShowAddDialog(false);
                  setEditingSource(null);
                }}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search and Bulk Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search sources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              {selectedSources.length > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">
                    {selectedSources.length} selected
                  </span>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction('enable')}>
                    <Play className="w-3 h-3 mr-1" />
                    Enable
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction('disable')}>
                    <Pause className="w-3 h-3 mr-1" />
                    Disable
                  </Button>
                  <Button
                    size="sm"
                    variant="default"
                    onClick={() => window.location.href = '/dashboard/pipelines'}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Zap className="w-3 h-3 mr-1" />
                    Start Pipeline
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button size="sm" variant="outline" className="text-red-600">
                        <Trash2 className="w-3 h-3 mr-1" />
                        Delete
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Sources</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete {selectedSources.length} selected sources? 
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleBulkAction('delete')}>
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedSources.length === filteredSources.length && filteredSources.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Processed</TableHead>
                <TableHead>Documents</TableHead>
                <TableHead>Enabled</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                    Loading sources...
                  </TableCell>
                </TableRow>
              ) : filteredSources.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="p-0">
                    {sources.length === 0 ? (
                      <EmptyState
                        title="No Data Sources Configured"
                        description="Get started by adding your first data source. You can add websites, PDF files, or text documents to build your knowledge base."
                        icon={<Plus className="w-8 h-8 text-gray-400" />}
                        actionLabel="Add Your First Source"
                        actionIcon={<Plus className="w-4 h-4" />}
                        onAction={() => setShowAddDialog(true)}
                        secondaryActionLabel="View Documentation"
                        secondaryActionIcon={<AlertCircle className="w-4 h-4" />}
                        onSecondaryAction={() => window.open('/docs/sources', '_blank')}
                      />
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        No sources match your search criteria
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                filteredSources.map((source) => (
                  <TableRow key={source.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedSources.includes(source.id!)}
                        onCheckedChange={(checked) => handleSelectSource(source.id!, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getSourceTypeIcon(source.source_type)}</span>
                        <div>
                          <div className="font-medium">{source.name}</div>
                          <div className="text-sm text-gray-500">
                            {source.url || source.path || 'No location specified'}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{source.source_type}</Badge>
                    </TableCell>
                    <TableCell>{getStatusBadge(source.status)}</TableCell>
                    <TableCell>
                      {source.last_processed ? formatDate(source.last_processed) : 'Never'}
                    </TableCell>
                    <TableCell>
                      {source.metadata?.documents || 0}
                    </TableCell>
                    <TableCell>
                      <Checkbox
                        checked={source.enabled}
                        onCheckedChange={(checked) => handleToggleSource(source.id!, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => {
                            setEditingSource(source);
                            setShowAddDialog(true);
                          }}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Reprocess
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => handleDeleteSource(source.id!)}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
