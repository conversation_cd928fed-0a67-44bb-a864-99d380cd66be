'use client';

import { useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Sidebar } from '@/components/layout/Sidebar';
import { Header } from '@/components/layout/Header';
import { Toaster } from '@/components/ui/sonner';
import { OnboardingWizard } from '@/components/onboarding/OnboardingWizard';
import { ContextualHelp } from '@/components/help/ContextualHelp';
import { useOnboarding } from '@/hooks/useOnboarding';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const onboarding = useOnboarding();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <ProtectedRoute>
      <div className="h-screen flex bg-gray-50">
        {/* Sidebar */}
        <Sidebar
          collapsed={sidebarCollapsed}
          onToggle={toggleSidebar}
          mobileOpen={mobileMenuOpen}
          onMobileToggle={toggleMobileMenu}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <Header
            title="Dashboard"
            description="RAG Chatbot Administration Panel"
            onMobileMenuToggle={toggleMobileMenu}
            rightContent={<ContextualHelp />}
          />

          {/* Page Content */}
          <main className="flex-1 overflow-auto">
            {children}
          </main>
        </div>
      </div>

      {/* Toast Notifications */}
      <Toaster />

      {/* Onboarding Wizard */}
      <OnboardingWizard
        open={onboarding.state.showWizard}
        onOpenChange={onboarding.hideOnboardingWizard}
        onComplete={onboarding.completeOnboarding}
      />
    </ProtectedRoute>
  );
}
