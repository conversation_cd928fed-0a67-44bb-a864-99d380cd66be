import { useState, useEffect } from 'react';

interface OnboardingState {
  hasSeenWelcome: boolean;
  hasAddedSource: boolean;
  hasRunPipeline: boolean;
  hasTestedChat: boolean;
  isComplete: boolean;
  showWizard: boolean;
}

const ONBOARDING_STORAGE_KEY = 'rag-chatbot-onboarding';

const defaultState: OnboardingState = {
  hasSeenWelcome: false,
  hasAddedSource: false,
  hasRunPipeline: false,
  hasTestedChat: false,
  isComplete: false,
  showWizard: false,
};

export function useOnboarding() {
  const [state, setState] = useState<OnboardingState>(defaultState);

  // Load onboarding state from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(ONBOARDING_STORAGE_KEY);
      if (stored) {
        const parsedState = JSON.parse(stored);
        setState(parsedState);
        
        // Show wizard if user hasn't completed onboarding
        if (!parsedState.isComplete && !parsedState.hasSeenWelcome) {
          setState(prev => ({ ...prev, showWizard: true }));
        }
      } else {
        // First time user - show wizard
        setState(prev => ({ ...prev, showWizard: true }));
      }
    } catch (error) {
      console.error('Failed to load onboarding state:', error);
      setState(prev => ({ ...prev, showWizard: true }));
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(ONBOARDING_STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.error('Failed to save onboarding state:', error);
    }
  }, [state]);

  const updateState = (updates: Partial<OnboardingState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const markStepComplete = (step: keyof OnboardingState) => {
    setState(prev => ({ ...prev, [step]: true }));
  };

  const completeOnboarding = () => {
    setState(prev => ({
      ...prev,
      isComplete: true,
      showWizard: false,
      hasSeenWelcome: true,
    }));
  };

  const resetOnboarding = () => {
    setState(defaultState);
    localStorage.removeItem(ONBOARDING_STORAGE_KEY);
  };

  const showOnboardingWizard = () => {
    setState(prev => ({ ...prev, showWizard: true }));
  };

  const hideOnboardingWizard = () => {
    setState(prev => ({ ...prev, showWizard: false }));
  };

  const shouldShowGuidance = () => {
    return !state.isComplete;
  };

  const getNextStep = () => {
    if (!state.hasAddedSource) {
      return {
        title: 'Add your first data source',
        description: 'Start by adding a website, PDF, or text file to build your knowledge base.',
        action: 'Go to Sources',
        href: '/dashboard/sources',
      };
    }
    
    if (!state.hasRunPipeline) {
      return {
        title: 'Run the pipeline',
        description: 'Process your data sources to make them searchable by your chatbot.',
        action: 'Start Pipeline',
        href: '/dashboard/pipelines',
      };
    }
    
    if (!state.hasTestedChat) {
      return {
        title: 'Test your chatbot',
        description: 'Try asking questions to see how your chatbot responds.',
        action: 'Open Chat',
        href: '/dashboard/chat',
      };
    }
    
    return null;
  };

  const getProgress = () => {
    const steps = [
      state.hasSeenWelcome,
      state.hasAddedSource,
      state.hasRunPipeline,
      state.hasTestedChat,
    ];
    
    const completedSteps = steps.filter(Boolean).length;
    return (completedSteps / steps.length) * 100;
  };

  return {
    state,
    updateState,
    markStepComplete,
    completeOnboarding,
    resetOnboarding,
    showOnboardingWizard,
    hideOnboardingWizard,
    shouldShowGuidance,
    getNextStep,
    getProgress,
  };
}
