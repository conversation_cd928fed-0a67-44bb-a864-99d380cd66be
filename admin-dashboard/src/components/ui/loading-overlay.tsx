'use client';

import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface LoadingOverlayProps {
  loading: boolean;
  text?: string;
  fullScreen?: boolean;
  progress?: number;
  className?: string;
  spinnerSize?: 'sm' | 'md' | 'lg';
  transparent?: boolean;
}

export function LoadingOverlay({
  loading,
  text = 'Loading...',
  fullScreen = false,
  progress,
  className,
  spinnerSize = 'md',
  transparent = false,
}: LoadingOverlayProps) {
  if (!loading) return null;

  const spinnerSizeClass = {
    sm: 'w-6 h-6',
    md: 'w-10 h-10',
    lg: 'w-16 h-16',
  };

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center',
        fullScreen
          ? 'fixed inset-0 z-50'
          : 'absolute inset-0 z-10',
        transparent
          ? 'bg-white/70 backdrop-blur-sm'
          : 'bg-white/90',
        className
      )}
    >
      <Loader2 className={cn('animate-spin text-blue-600', spinnerSizeClass[spinnerSize])} />
      {text && <p className="mt-4 text-gray-700 font-medium">{text}</p>}
      {typeof progress === 'number' && (
        <div className="w-64 mt-4">
          <Progress value={progress} className="h-2" />
          <p className="text-xs text-center mt-1 text-gray-500">{Math.round(progress)}% complete</p>
        </div>
      )}
    </div>
  );
}

interface LoadingButtonProps {
  loading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export function LoadingButton({
  loading,
  children,
  loadingText = 'Loading...',
  className,
  disabled,
  onClick,
  variant = 'default',
  size = 'default',
}: LoadingButtonProps) {
  const variantClasses = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    link: 'text-primary underline-offset-4 hover:underline',
  };

  const sizeClasses = {
    default: 'h-10 px-4 py-2',
    sm: 'h-9 rounded-md px-3',
    lg: 'h-11 rounded-md px-8',
    icon: 'h-10 w-10',
  };

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {loadingText}
        </>
      ) : (
        children
      )}
    </button>
  );
}
