'use client';

import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  RefreshCw,
  Pause,
  Play,
  Zap,
} from 'lucide-react';

export type StatusType = 
  | 'success' 
  | 'pending' 
  | 'processing' 
  | 'warning' 
  | 'error' 
  | 'disabled' 
  | 'active' 
  | 'inactive';

interface StatusBadgeProps {
  status: StatusType;
  text?: string;
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const statusConfig = {
  success: {
    icon: CheckCircle,
    className: 'bg-green-100 text-green-800 border-green-200',
    defaultText: 'Success',
  },
  pending: {
    icon: Clock,
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    defaultText: 'Pending',
  },
  processing: {
    icon: RefreshCw,
    className: 'bg-blue-100 text-blue-800 border-blue-200',
    defaultText: 'Processing',
    animated: true,
  },
  warning: {
    icon: AlertCircle,
    className: 'bg-orange-100 text-orange-800 border-orange-200',
    defaultText: 'Warning',
  },
  error: {
    icon: XCircle,
    className: 'bg-red-100 text-red-800 border-red-200',
    defaultText: 'Error',
  },
  disabled: {
    icon: Pause,
    className: 'bg-gray-100 text-gray-600 border-gray-200',
    defaultText: 'Disabled',
  },
  active: {
    icon: Play,
    className: 'bg-green-100 text-green-800 border-green-200',
    defaultText: 'Active',
  },
  inactive: {
    icon: Pause,
    className: 'bg-gray-100 text-gray-600 border-gray-200',
    defaultText: 'Inactive',
  },
};

export function StatusBadge({ 
  status, 
  text, 
  animated = false, 
  size = 'md',
  className 
}: StatusBadgeProps) {
  const config = statusConfig[status];
  const Icon = config.icon;
  const displayText = text || config.defaultText;
  const shouldAnimate = animated || config.animated;

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5',
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  return (
    <Badge
      variant="outline"
      className={cn(
        'inline-flex items-center gap-1.5 font-medium border',
        config.className,
        sizeClasses[size],
        className
      )}
    >
      <Icon 
        className={cn(
          iconSizes[size],
          shouldAnimate && 'animate-spin'
        )} 
      />
      {displayText}
    </Badge>
  );
}

// Specialized status badges for common use cases
export function PipelineStatusBadge({ status }: { status: string }) {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'success':
      return <StatusBadge status="success" text="Completed" />;
    case 'processing':
    case 'running':
      return <StatusBadge status="processing" text="Processing" animated />;
    case 'pending':
    case 'queued':
      return <StatusBadge status="pending" text="Pending" />;
    case 'failed':
    case 'error':
      return <StatusBadge status="error" text="Failed" />;
    case 'cancelled':
    case 'stopped':
      return <StatusBadge status="warning" text="Cancelled" />;
    default:
      return <StatusBadge status="inactive" text={status} />;
  }
}

export function SourceStatusBadge({ status, enabled }: { status: string; enabled?: boolean }) {
  if (enabled === false) {
    return <StatusBadge status="disabled" text="Disabled" />;
  }

  switch (status.toLowerCase()) {
    case 'completed':
    case 'processed':
      return <StatusBadge status="success" text="Ready" />;
    case 'processing':
      return <StatusBadge status="processing" text="Processing" animated />;
    case 'pending':
      return <StatusBadge status="pending" text="Pending" />;
    case 'failed':
    case 'error':
      return <StatusBadge status="error" text="Error" />;
    default:
      return <StatusBadge status="inactive" text={status} />;
  }
}

export function HealthStatusBadge({ health }: { health: string }) {
  switch (health.toLowerCase()) {
    case 'healthy':
    case 'good':
      return <StatusBadge status="success" text="Healthy" />;
    case 'degraded':
    case 'warning':
      return <StatusBadge status="warning" text="Degraded" />;
    case 'unhealthy':
    case 'critical':
    case 'down':
      return <StatusBadge status="error" text="Unhealthy" />;
    default:
      return <StatusBadge status="inactive" text="Unknown" />;
  }
}
