'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  CheckCircle,
  AlertCircle,
  XCircle,
  Info,
  X,
  ArrowRight,
} from 'lucide-react';

export type NotificationType = 'success' | 'warning' | 'error' | 'info';

interface NotificationBannerProps {
  type: NotificationType;
  title: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

const notificationConfig = {
  success: {
    icon: CheckCircle,
    className: 'bg-green-50 border-green-200 text-green-800',
    iconClassName: 'text-green-600',
  },
  warning: {
    icon: AlertCircle,
    className: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    iconClassName: 'text-yellow-600',
  },
  error: {
    icon: XCircle,
    className: 'bg-red-50 border-red-200 text-red-800',
    iconClassName: 'text-red-600',
  },
  info: {
    icon: Info,
    className: 'bg-blue-50 border-blue-200 text-blue-800',
    iconClassName: 'text-blue-600',
  },
};

export function NotificationBanner({
  type,
  title,
  message,
  action,
  dismissible = true,
  onDismiss,
  className,
}: NotificationBannerProps) {
  const [dismissed, setDismissed] = useState(false);
  const config = notificationConfig[type];
  const Icon = config.icon;

  const handleDismiss = () => {
    setDismissed(true);
    onDismiss?.();
  };

  if (dismissed) return null;

  return (
    <div
      className={cn(
        'border rounded-lg p-4',
        config.className,
        className
      )}
    >
      <div className="flex items-start">
        <Icon className={cn('w-5 h-5 mt-0.5 mr-3 flex-shrink-0', config.iconClassName)} />
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium">{title}</h3>
          {message && (
            <p className="mt-1 text-sm opacity-90">{message}</p>
          )}
          {action && (
            <div className="mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={action.onClick}
                className="text-current border-current hover:bg-current hover:text-white"
              >
                {action.label}
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )}
        </div>
        {dismissible && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="text-current hover:bg-current hover:text-white ml-2 flex-shrink-0"
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

// Specialized notification components
export function SuccessNotification({
  title,
  message,
  action,
  onDismiss,
}: Omit<NotificationBannerProps, 'type'>) {
  return (
    <NotificationBanner
      type="success"
      title={title}
      message={message}
      action={action}
      onDismiss={onDismiss}
    />
  );
}

export function ErrorNotification({
  title,
  message,
  action,
  onDismiss,
}: Omit<NotificationBannerProps, 'type'>) {
  return (
    <NotificationBanner
      type="error"
      title={title}
      message={message}
      action={action}
      onDismiss={onDismiss}
    />
  );
}

export function WarningNotification({
  title,
  message,
  action,
  onDismiss,
}: Omit<NotificationBannerProps, 'type'>) {
  return (
    <NotificationBanner
      type="warning"
      title={title}
      message={message}
      action={action}
      onDismiss={onDismiss}
    />
  );
}

export function InfoNotification({
  title,
  message,
  action,
  onDismiss,
}: Omit<NotificationBannerProps, 'type'>) {
  return (
    <NotificationBanner
      type="info"
      title={title}
      message={message}
      action={action}
      onDismiss={onDismiss}
    />
  );
}
