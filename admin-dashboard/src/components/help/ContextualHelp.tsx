'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  She<PERSON><PERSON><PERSON>er,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { HelpCircle, Search, BookOpen, Lightbulb, ArrowRight, X } from 'lucide-react';

interface HelpTopic {
  id: string;
  title: string;
  category: string;
  content: React.ReactNode;
  tags: string[];
}

const helpTopics: HelpTopic[] = [
  {
    id: 'data-sources',
    title: 'Adding Data Sources',
    category: 'basics',
    tags: ['sources', 'data', 'add', 'website', 'pdf', 'text'],
    content: (
      <div className="space-y-4">
        <p>Data sources are the foundation of your RAG chatbot. You can add three types of sources:</p>
        <ul className="list-disc pl-5 space-y-2">
          <li><strong>Websites</strong>: Enter the URL and set crawl depth to collect web content</li>
          <li><strong>PDF Files</strong>: Specify the path to PDF documents</li>
          <li><strong>Text Files</strong>: Specify the path to plain text documents</li>
        </ul>
        <p>To add a new source:</p>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Go to the Sources page</li>
          <li>Click "Add Source" button</li>
          <li>Fill in the required fields</li>
          <li>Click "Create Source"</li>
        </ol>
        <div className="bg-blue-50 p-3 rounded-md mt-4">
          <p className="text-sm text-blue-800">
            <strong>Tip:</strong> Start with a small crawl depth (1-2) for websites to avoid collecting too much data initially.
          </p>
        </div>
      </div>
    ),
  },
  {
    id: 'collection-methods',
    title: 'Collection Methods Explained',
    category: 'basics',
    tags: ['collection', 'methods', 'crawling', 'processing', 'strategy'],
    content: (
      <div className="space-y-4">
        <p>Collection methods determine how data is gathered and processed from your sources:</p>
        <ul className="list-disc pl-5 space-y-3">
          <li>
            <strong>Standard Collection</strong>
            <p className="text-sm text-gray-600">Balanced approach suitable for most websites and documents</p>
          </li>
          <li>
            <strong>Deep Crawl</strong>
            <p className="text-sm text-gray-600">Comprehensive crawling with detailed content extraction</p>
          </li>
          <li>
            <strong>Content Only</strong>
            <p className="text-sm text-gray-600">Focus on main content, skip navigation and metadata</p>
          </li>
          <li>
            <strong>Selective</strong>
            <p className="text-sm text-gray-600">Custom filtering and selective content extraction</p>
          </li>
        </ul>
        <div className="bg-blue-50 p-3 rounded-md mt-4">
          <p className="text-sm text-blue-800">
            <strong>Tip:</strong> Use presets like "Legal Documents" or "Technical Docs" to automatically configure optimal settings for specific content types.
          </p>
        </div>
      </div>
    ),
  },
  {
    id: 'pipeline-execution',
    title: 'Running the Pipeline',
    category: 'basics',
    tags: ['pipeline', 'execution', 'processing', 'start'],
    content: (
      <div className="space-y-4">
        <p>The pipeline processes your data sources and prepares them for use by the chatbot:</p>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Go to the Pipelines page</li>
          <li>Select a pipeline mode:
            <ul className="list-disc pl-5 mt-2">
              <li><strong>Full Processing</strong>: Process all enabled sources from scratch</li>
              <li><strong>Incremental Update</strong>: Process only new or changed content</li>
              <li><strong>Reprocess Only</strong>: Reprocess existing content without recrawling</li>
              <li><strong>Validation Only</strong>: Check sources without processing</li>
            </ul>
          </li>
          <li>Click "Start Pipeline" to begin processing</li>
        </ol>
        <div className="bg-amber-50 p-3 rounded-md mt-4">
          <p className="text-sm text-amber-800">
            <strong>Important:</strong> You must add and enable at least one data source before starting the pipeline.
          </p>
        </div>
      </div>
    ),
  },
  {
    id: 'source-selection',
    title: 'Selecting Sources for Processing',
    category: 'advanced',
    tags: ['sources', 'selection', 'pipeline', 'processing'],
    content: (
      <div className="space-y-4">
        <p>You can select specific sources to process in the pipeline:</p>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Go to the Sources page</li>
          <li>Use the checkboxes to select one or more sources</li>
          <li>Click "Start Pipeline" in the action bar</li>
          <li>Alternatively, go to the Pipelines page where your selected sources will be shown</li>
        </ol>
        <p>If no specific sources are selected, the pipeline will process all enabled sources.</p>
        <div className="bg-blue-50 p-3 rounded-md mt-4">
          <p className="text-sm text-blue-800">
            <strong>Tip:</strong> Use bulk selection to quickly select multiple sources of the same type.
          </p>
        </div>
      </div>
    ),
  },
];

export function ContextualHelp() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  const filteredTopics = helpTopics.filter(topic => {
    const matchesSearch = searchTerm === '' || 
      topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      topic.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = activeTab === 'all' || topic.category === activeTab;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className="rounded-full">
          <HelpCircle className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px] p-0">
        <div className="flex flex-col h-full">
          <SheetHeader className="p-6 pb-2">
            <SheetTitle className="flex items-center gap-2">
              <BookOpen className="w-5 h-5" />
              Help & Documentation
            </SheetTitle>
            <SheetDescription>
              Find answers and learn how to use the RAG chatbot admin dashboard
            </SheetDescription>
          </SheetHeader>
          
          <div className="p-6 pt-2 pb-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="Search help topics..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="px-6">
            <TabsList>
              <TabsTrigger value="all">All Topics</TabsTrigger>
              <TabsTrigger value="basics">Basics</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <ScrollArea className="flex-1 p-6 pt-4">
            {filteredTopics.length === 0 ? (
              <div className="text-center py-8">
                <Lightbulb className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <h3 className="text-lg font-medium text-gray-900">No results found</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Try searching with different keywords
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredTopics.map((topic) => (
                  <div key={topic.id} className="border rounded-lg overflow-hidden">
                    <div className="bg-gray-50 px-4 py-3 border-b">
                      <h3 className="font-medium">{topic.title}</h3>
                    </div>
                    <div className="p-4">
                      {topic.content}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </SheetContent>
    </Sheet>
  );
}
