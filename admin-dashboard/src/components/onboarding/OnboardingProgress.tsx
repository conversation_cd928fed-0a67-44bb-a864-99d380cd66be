'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useOnboarding } from '@/hooks/useOnboarding';
import { ArrowRight, CheckCircle, X } from 'lucide-react';

export function OnboardingProgress() {
  const onboarding = useOnboarding();

  if (onboarding.state.isComplete) {
    return null;
  }

  const nextStep = onboarding.getNextStep();
  const progress = onboarding.getProgress();

  return (
    <Card className="mb-6 border-blue-200 bg-blue-50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg text-blue-900">Getting Started</CardTitle>
            <Badge variant="outline" className="bg-white">
              {Math.round(progress)}% Complete
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onboarding.completeOnboarding}
            className="text-blue-600 hover:text-blue-800"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        <CardDescription className="text-blue-700">
          Complete these steps to set up your RAG chatbot system
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Progress value={progress} className="w-full" />
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          <div className={`flex items-center gap-2 p-2 rounded-md ${
            onboarding.state.hasSeenWelcome ? 'bg-green-100 text-green-800' : 'bg-white text-gray-600'
          }`}>
            <CheckCircle className={`w-4 h-4 ${
              onboarding.state.hasSeenWelcome ? 'text-green-600' : 'text-gray-400'
            }`} />
            <span className="text-sm font-medium">Welcome</span>
          </div>
          
          <div className={`flex items-center gap-2 p-2 rounded-md ${
            onboarding.state.hasAddedSource ? 'bg-green-100 text-green-800' : 'bg-white text-gray-600'
          }`}>
            <CheckCircle className={`w-4 h-4 ${
              onboarding.state.hasAddedSource ? 'text-green-600' : 'text-gray-400'
            }`} />
            <span className="text-sm font-medium">Add Source</span>
          </div>
          
          <div className={`flex items-center gap-2 p-2 rounded-md ${
            onboarding.state.hasRunPipeline ? 'bg-green-100 text-green-800' : 'bg-white text-gray-600'
          }`}>
            <CheckCircle className={`w-4 h-4 ${
              onboarding.state.hasRunPipeline ? 'text-green-600' : 'text-gray-400'
            }`} />
            <span className="text-sm font-medium">Run Pipeline</span>
          </div>
          
          <div className={`flex items-center gap-2 p-2 rounded-md ${
            onboarding.state.hasTestedChat ? 'bg-green-100 text-green-800' : 'bg-white text-gray-600'
          }`}>
            <CheckCircle className={`w-4 h-4 ${
              onboarding.state.hasTestedChat ? 'text-green-600' : 'text-gray-400'
            }`} />
            <span className="text-sm font-medium">Test Chat</span>
          </div>
        </div>

        {nextStep && (
          <div className="flex items-center justify-between p-3 bg-white rounded-md border border-blue-200">
            <div>
              <h4 className="font-medium text-blue-900">{nextStep.title}</h4>
              <p className="text-sm text-blue-700">{nextStep.description}</p>
            </div>
            <Button
              size="sm"
              onClick={() => {
                if (nextStep.href) {
                  window.location.href = nextStep.href;
                }
              }}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {nextStep.action}
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
