'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CheckCircle,
  Circle,
  ArrowRight,
  ArrowLeft,
  Plus,
  Play,
  Settings,
  BookOpen,
  Zap,
  Target,
} from 'lucide-react';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
  action?: {
    label: string;
    href?: string;
    onClick?: () => void;
  };
}

interface OnboardingWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: () => void;
}

export function OnboardingWizard({ open, onOpenChange, onComplete }: OnboardingWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState<OnboardingStep[]>([
    {
      id: 'welcome',
      title: 'Welcome to RAG Chatbot Admin',
      description: 'Let\'s get you started with setting up your knowledge base and chatbot system.',
      icon: <Target className="w-6 h-6" />,
      completed: false,
    },
    {
      id: 'add-source',
      title: 'Add Your First Data Source',
      description: 'Add a website, PDF, or text file to start building your knowledge base.',
      icon: <Plus className="w-6 h-6" />,
      completed: false,
      action: {
        label: 'Add Data Source',
        href: '/dashboard/sources',
      },
    },
    {
      id: 'configure-collection',
      title: 'Configure Collection Method',
      description: 'Choose how your data should be collected and processed for optimal results.',
      icon: <Settings className="w-6 h-6" />,
      completed: false,
    },
    {
      id: 'run-pipeline',
      title: 'Run Your First Pipeline',
      description: 'Process your data sources to make them searchable by your chatbot.',
      icon: <Play className="w-6 h-6" />,
      completed: false,
      action: {
        label: 'Start Pipeline',
        href: '/dashboard/pipelines',
      },
    },
    {
      id: 'test-chatbot',
      title: 'Test Your Chatbot',
      description: 'Try asking questions to see how your chatbot responds with the processed data.',
      icon: <Zap className="w-6 h-6" />,
      completed: false,
      action: {
        label: 'Open Chat',
        href: '/dashboard/chat',
      },
    },
    {
      id: 'learn-more',
      title: 'Learn More',
      description: 'Explore advanced features and best practices for optimizing your chatbot.',
      icon: <BookOpen className="w-6 h-6" />,
      completed: false,
      action: {
        label: 'View Documentation',
        href: '/docs',
      },
    },
  ]);

  const progress = (steps.filter(step => step.completed).length / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepComplete = (stepId: string) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, completed: true } : step
    ));
  };

  const handleFinish = () => {
    onComplete();
    onOpenChange(false);
  };

  const currentStepData = steps[currentStep];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
              {currentStepData.icon}
            </div>
            Getting Started
          </DialogTitle>
          <DialogDescription>
            Follow these steps to set up your RAG chatbot system
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>

          {/* Step Navigation */}
          <div className="flex items-center justify-center space-x-2 overflow-x-auto pb-2">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <button
                  onClick={() => setCurrentStep(index)}
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ${
                    index === currentStep
                      ? 'border-blue-500 bg-blue-500 text-white'
                      : step.completed
                      ? 'border-green-500 bg-green-500 text-white'
                      : 'border-gray-300 bg-white text-gray-400'
                  }`}
                >
                  {step.completed ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    <span className="text-xs font-medium">{index + 1}</span>
                  )}
                </button>
                {index < steps.length - 1 && (
                  <div className={`w-8 h-0.5 mx-1 ${
                    step.completed ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>

          {/* Current Step Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                  {currentStepData.icon}
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{currentStepData.title}</h3>
                  <Badge variant="outline" className="mt-1">
                    Step {currentStep + 1} of {steps.length}
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">{currentStepData.description}</p>
              
              {currentStepData.action && (
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => {
                      if (currentStepData.action?.href) {
                        window.location.href = currentStepData.action.href;
                      }
                      if (currentStepData.action?.onClick) {
                        currentStepData.action.onClick();
                      }
                      handleStepComplete(currentStepData.id);
                    }}
                    className="flex items-center gap-2"
                  >
                    {currentStepData.action.label}
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleStepComplete(currentStepData.id)}
                  >
                    Mark as Complete
                  </Button>
                </div>
              )}

              {currentStepData.id === 'welcome' && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <h4 className="font-medium text-blue-900 mb-2">What you'll accomplish:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Set up your first data source</li>
                    <li>• Configure data collection methods</li>
                    <li>• Process data through the pipeline</li>
                    <li>• Test your chatbot with real queries</li>
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>
          
          <div className="flex gap-2">
            <Button variant="ghost" onClick={() => onOpenChange(false)}>
              Skip for now
            </Button>
            {currentStep === steps.length - 1 ? (
              <Button onClick={handleFinish}>
                Finish Setup
              </Button>
            ) : (
              <Button onClick={handleNext}>
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
