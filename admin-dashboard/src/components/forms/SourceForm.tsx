'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, HelpCircle } from 'lucide-react';
import { DataSource, SourceType, AdminSourceRequest, CollectionMethod, CrawlingStrategy, ProcessingPreset, CollectionConfig } from '@/lib/types';
import { sourceService, pipelineService } from '@/lib/api/services';
import { toast } from 'sonner';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const sourceSchema = z.object({
  name: z.string().min(1, 'Name is required').max(200, 'Name too long'),
  source_type: z.nativeEnum(SourceType),
  url: z.string().url('Invalid URL').optional().or(z.literal('')),
  path: z.string().optional(),
  crawl_depth: z.number().min(1).max(10).optional(),
  enabled: z.boolean(),
  collection_config: z.object({
    method: z.nativeEnum(CollectionMethod),
    crawling_strategy: z.nativeEnum(CrawlingStrategy).optional(),
    preset: z.nativeEnum(ProcessingPreset).optional(),
    chunk_size: z.number().min(100).max(5000).optional(),
    chunk_overlap: z.number().min(0).max(1000).optional(),
    respect_robots_txt: z.boolean().optional(),
    max_file_size_mb: z.number().min(1).max(100).optional(),
    allowed_content_types: z.array(z.string()).optional(),
    excluded_patterns: z.array(z.string()).optional(),
    custom_separators: z.array(z.string()).optional(),
    language_filter: z.array(z.string()).optional(),
  }).optional(),
  metadata: z.record(z.any()).optional(),
});

type SourceFormData = z.infer<typeof sourceSchema>;

interface SourceFormProps {
  source?: DataSource;
  onSuccess: (source: DataSource) => void;
  onCancel: () => void;
}

export function SourceForm({ source, onSuccess, onCancel }: SourceFormProps) {
  const isEditing = !!source;
  const [collectionMethods, setCollectionMethods] = useState<any>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [loadingMethods, setLoadingMethods] = useState(true);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<SourceFormData>({
    resolver: zodResolver(sourceSchema),
    defaultValues: {
      name: source?.name || '',
      source_type: source?.source_type || SourceType.WEBSITE,
      url: source?.url || '',
      path: source?.path || '',
      crawl_depth: source?.crawl_depth || 2,
      enabled: source?.enabled ?? true,
      collection_config: source?.collection_config || {
        method: CollectionMethod.STANDARD,
        crawling_strategy: CrawlingStrategy.BREADTH_FIRST,
        chunk_size: 1000,
        chunk_overlap: 200,
        respect_robots_txt: true,
        max_file_size_mb: 10,
      },
      metadata: source?.metadata || {},
    },
  });

  // Load collection methods on component mount
  useEffect(() => {
    const loadCollectionMethods = async () => {
      try {
        const methods = await pipelineService.getCollectionMethods();
        setCollectionMethods(methods);
      } catch (error) {
        console.error('Failed to load collection methods:', error);
        toast.error('Failed to load collection methods');
      } finally {
        setLoadingMethods(false);
      }
    };

    loadCollectionMethods();
  }, []);

  const sourceType = watch('source_type');
  const collectionMethod = watch('collection_config.method');
  const collectionPreset = watch('collection_config.preset');

  const onSubmit = async (data: SourceFormData) => {
    try {
      const requestData: AdminSourceRequest = {
        name: data.name,
        source_type: data.source_type,
        url: data.url || undefined,
        path: data.path || undefined,
        crawl_depth: data.crawl_depth,
        enabled: data.enabled,
        collection_config: data.collection_config,
        metadata: data.metadata || {},
      };

      let result: DataSource;
      if (isEditing && source?.id) {
        result = await sourceService.updateSource(source.id, requestData);
        toast.success('Source updated successfully');
      } else {
        result = await sourceService.createSource(requestData);
        toast.success('Source created successfully');
      }

      onSuccess(result);
    } catch (error: any) {
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} source: ${error.message}`);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Configure the basic settings for your data source
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              placeholder="Enter source name"
              {...register('name')}
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="source_type">Source Type *</Label>
            <Select
              value={sourceType}
              onValueChange={(value) => setValue('source_type', value as SourceType)}
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select source type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={SourceType.WEBSITE}>Website</SelectItem>
                <SelectItem value={SourceType.PDF}>PDF Files</SelectItem>
                <SelectItem value={SourceType.TEXT}>Text Files</SelectItem>
              </SelectContent>
            </Select>
            {errors.source_type && (
              <p className="text-sm text-red-600">{errors.source_type.message}</p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="enabled"
              checked={watch('enabled')}
              onCheckedChange={(checked) => setValue('enabled', checked)}
              disabled={isSubmitting}
            />
            <Label htmlFor="enabled">Enable this source</Label>
          </div>
        </CardContent>
      </Card>

      {/* Source Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Source Configuration</CardTitle>
          <CardDescription>
            Configure how to access and process this data source
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {sourceType === SourceType.WEBSITE && (
            <>
              <div className="space-y-2">
                <Label htmlFor="url">Website URL *</Label>
                <Input
                  id="url"
                  type="url"
                  placeholder="https://example.com"
                  {...register('url')}
                  disabled={isSubmitting}
                />
                {errors.url && (
                  <p className="text-sm text-red-600">{errors.url.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="crawl_depth">Crawl Depth</Label>
                <Input
                  id="crawl_depth"
                  type="number"
                  min="1"
                  max="10"
                  placeholder="2"
                  {...register('crawl_depth', { valueAsNumber: true })}
                  disabled={isSubmitting}
                />
                <p className="text-sm text-gray-500">
                  How many levels deep to crawl (1-10)
                </p>
                {errors.crawl_depth && (
                  <p className="text-sm text-red-600">{errors.crawl_depth.message}</p>
                )}
              </div>
            </>
          )}

          {(sourceType === SourceType.PDF || sourceType === SourceType.TEXT) && (
            <div className="space-y-2">
              <Label htmlFor="path">File Path *</Label>
              <Input
                id="path"
                placeholder="/path/to/files/"
                {...register('path')}
                disabled={isSubmitting}
              />
              <p className="text-sm text-gray-500">
                Path to the directory containing your files
              </p>
              {errors.path && (
                <p className="text-sm text-red-600">{errors.path.message}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Collection Method Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Collection Method
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="w-4 h-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Choose how this data source should be collected and processed</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </CardTitle>
          <CardDescription>
            Configure how data is collected and processed from this source
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {loadingMethods ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span className="ml-2">Loading collection methods...</span>
            </div>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="collection_method">Collection Method</Label>
                <Select
                  value={collectionMethod}
                  onValueChange={(value) => {
                    setValue('collection_config.method', value as CollectionMethod);
                    // Apply default config for selected method
                    const method = collectionMethods?.collection_methods?.find((m: any) => m.value === value);
                    if (method?.default_config) {
                      Object.entries(method.default_config).forEach(([key, val]) => {
                        setValue(`collection_config.${key}` as any, val);
                      });
                    }
                  }}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select collection method" />
                  </SelectTrigger>
                  <SelectContent>
                    {collectionMethods?.collection_methods?.map((method: any) => (
                      <SelectItem key={method.value} value={method.value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{method.label}</span>
                          <span className="text-xs text-gray-500">{method.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {collectionMethod && collectionMethods?.collection_methods && (
                  <div className="mt-2">
                    {(() => {
                      const method = collectionMethods.collection_methods.find((m: any) => m.value === collectionMethod);
                      return method ? (
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                          <p className="text-sm text-blue-800 mb-2">{method.description}</p>
                          <div className="flex flex-wrap gap-1">
                            <span className="text-xs text-blue-600">Recommended for:</span>
                            {method.recommended_for.map((item: string, index: number) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {item}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      ) : null;
                    })()}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="processing_preset">Processing Preset (Optional)</Label>
                <Select
                  value={collectionPreset || ''}
                  onValueChange={(value) => {
                    if (value) {
                      setValue('collection_config.preset', value as ProcessingPreset);
                      // Apply preset config
                      const preset = collectionMethods?.processing_presets?.find((p: any) => p.value === value);
                      if (preset?.config) {
                        Object.entries(preset.config).forEach(([key, val]) => {
                          setValue(`collection_config.${key}` as any, val);
                        });
                      }
                    } else {
                      setValue('collection_config.preset', undefined);
                    }
                  }}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a preset (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No preset</SelectItem>
                    {collectionMethods?.processing_presets?.map((preset: any) => (
                      <SelectItem key={preset.value} value={preset.value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{preset.label}</span>
                          <span className="text-xs text-gray-500">{preset.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="w-full justify-between">
                    Advanced Collection Settings
                    <span className="text-xs text-gray-500">
                      {showAdvanced ? 'Hide' : 'Show'} advanced options
                    </span>
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 mt-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="chunk_size">Chunk Size</Label>
                      <Input
                        id="chunk_size"
                        type="number"
                        min="100"
                        max="5000"
                        {...register('collection_config.chunk_size', { valueAsNumber: true })}
                        disabled={isSubmitting}
                      />
                      <p className="text-xs text-gray-500">Text chunk size in characters</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="chunk_overlap">Chunk Overlap</Label>
                      <Input
                        id="chunk_overlap"
                        type="number"
                        min="0"
                        max="1000"
                        {...register('collection_config.chunk_overlap', { valueAsNumber: true })}
                        disabled={isSubmitting}
                      />
                      <p className="text-xs text-gray-500">Overlap between chunks</p>
                    </div>
                  </div>

                  {sourceType === SourceType.WEBSITE && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="crawling_strategy">Crawling Strategy</Label>
                        <Select
                          value={watch('collection_config.crawling_strategy') || ''}
                          onValueChange={(value) => setValue('collection_config.crawling_strategy', value as CrawlingStrategy)}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select crawling strategy" />
                          </SelectTrigger>
                          <SelectContent>
                            {collectionMethods?.crawling_strategies?.map((strategy: any) => (
                              <SelectItem key={strategy.value} value={strategy.value}>
                                <div className="flex flex-col">
                                  <span className="font-medium">{strategy.label}</span>
                                  <span className="text-xs text-gray-500">{strategy.description}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="respect_robots_txt"
                          checked={watch('collection_config.respect_robots_txt') ?? true}
                          onCheckedChange={(checked) => setValue('collection_config.respect_robots_txt', checked)}
                          disabled={isSubmitting}
                        />
                        <Label htmlFor="respect_robots_txt">Respect robots.txt</Label>
                      </div>
                    </>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="max_file_size">Max File Size (MB)</Label>
                    <Input
                      id="max_file_size"
                      type="number"
                      min="1"
                      max="100"
                      {...register('collection_config.max_file_size_mb', { valueAsNumber: true })}
                      disabled={isSubmitting}
                    />
                    <p className="text-xs text-gray-500">Maximum file size to process</p>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </>
          )}
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced Settings</CardTitle>
          <CardDescription>
            Optional metadata and advanced configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="metadata">Metadata (JSON)</Label>
            <Textarea
              id="metadata"
              placeholder='{"category": "legal", "priority": "high"}'
              defaultValue={JSON.stringify(source?.metadata || {}, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value || '{}');
                  setValue('metadata', parsed);
                } catch {
                  // Invalid JSON, ignore
                }
              }}
              disabled={isSubmitting}
              rows={4}
            />
            <p className="text-sm text-gray-500">
              Additional metadata in JSON format (optional)
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isEditing ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            isEditing ? 'Update Source' : 'Create Source'
          )}
        </Button>
      </div>
    </form>
  );
}
