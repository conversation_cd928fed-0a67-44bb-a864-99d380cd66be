"""Enhanced admin endpoints for comprehensive system management."""

from typing import List, Optional, Dict, Any

from fastapi import APIRouter, HTTPException, Depends, Security, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.models import (
    AdminSourceRequest,
    DataSource,
    ReindexRequest,
    MetricsResponse
)
from shared.config import get_settings
from shared.logging_config import get_logger
from admin_interface.services.system_monitor import SystemMonitor
from admin_interface.services.source_manager import AdminSourceManager
from admin_interface.services.pipeline_manager import PipelineManager, PipelineMode
from admin_interface.services.analytics_service import AnalyticsService
from admin_interface.services.config_manager import ConfigManager

logger = get_logger(__name__)
router = APIRouter()
security = HTTPBearer()

# Global service instances
_system_monitor = None
_source_manager = None
_pipeline_manager = None
_analytics_service = None
_config_manager = None


async def get_system_monitor() -> SystemMonitor:
    """Get the system monitor instance."""
    global _system_monitor
    if _system_monitor is None:
        _system_monitor = SystemMonitor()
        await _system_monitor.initialize()
    return _system_monitor


async def get_source_manager() -> AdminSourceManager:
    """Get the source manager instance."""
    global _source_manager
    if _source_manager is None:
        _source_manager = AdminSourceManager()
        await _source_manager.initialize()
    return _source_manager


async def get_pipeline_manager() -> PipelineManager:
    """Get the pipeline manager instance."""
    global _pipeline_manager
    if _pipeline_manager is None:
        _pipeline_manager = PipelineManager()
        await _pipeline_manager.initialize()
    return _pipeline_manager


async def get_analytics_service() -> AnalyticsService:
    """Get the analytics service instance."""
    global _analytics_service
    if _analytics_service is None:
        _analytics_service = AnalyticsService()
        await _analytics_service.initialize()
    return _analytics_service


async def get_config_manager() -> ConfigManager:
    """Get the config manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
        await _config_manager.initialize()
    return _config_manager


def verify_admin_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Verify admin authentication token."""
    settings = get_settings()

    if not settings.admin_api_key:
        # If no admin key is configured, allow access (development mode)
        return True

    if not credentials or credentials.credentials != settings.admin_api_key:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication credentials"
        )
    return True


@router.get("/sources", response_model=List[DataSource])
async def list_sources(
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """List all configured data sources."""
    try:
        sources = await source_manager.list_sources()
        logger.info(f"Listed {len(sources)} data sources")
        return sources
    except Exception as e:
        logger.error(f"Error listing sources: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error listing sources")


@router.get("/sources/{source_id}")
async def get_source(
    source_id: str,
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Get a specific data source."""
    try:
        source = await source_manager.get_source(source_id)
        if not source:
            raise HTTPException(status_code=404, detail="Source not found")
        return source
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting source {source_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error getting source")


@router.post("/sources", response_model=DataSource)
async def add_source(
    source_request: AdminSourceRequest,
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Add a new data source."""
    try:
        source_data = {
            "name": source_request.name,
            "source_type": source_request.source_type,
            "url": source_request.url,
            "path": source_request.path,
            "crawl_depth": source_request.crawl_depth,
            "enabled": source_request.enabled,
            "metadata": source_request.metadata
        }

        new_source = await source_manager.create_source(source_data)
        logger.info(f"Added new source: {new_source.name}")
        return new_source
    except Exception as e:
        logger.error(f"Error adding source: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/sources/{source_id}", response_model=DataSource)
async def update_source(
    source_id: str,
    source_request: AdminSourceRequest,
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Update an existing data source."""
    try:
        source_data = {
            "name": source_request.name,
            "source_type": source_request.source_type,
            "url": source_request.url,
            "path": source_request.path,
            "crawl_depth": source_request.crawl_depth,
            "enabled": source_request.enabled,
            "metadata": source_request.metadata
        }

        updated_source = await source_manager.update_source(source_id, source_data)
        logger.info(f"Updated source: {source_id}")
        return updated_source
    except Exception as e:
        logger.error(f"Error updating source: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/sources/{source_id}")
async def delete_source(
    source_id: str,
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Delete a data source."""
    try:
        success = await source_manager.delete_source(source_id)
        if not success:
            raise HTTPException(status_code=404, detail="Source not found")

        logger.info(f"Deleted source: {source_id}")
        return {"message": f"Source {source_id} deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting source: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sources/{source_id}/status")
async def get_source_status(
    source_id: str,
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Get processing status for a data source."""
    try:
        status = await source_manager.get_source_status(source_id)
        return status
    except Exception as e:
        logger.error(f"Error getting source status {source_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sources/{source_id}/validate")
async def validate_source(
    source_id: str,
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Validate a data source configuration."""
    try:
        validation_result = await source_manager.validate_source(source_id)
        return validation_result
    except Exception as e:
        logger.error(f"Error validating source {source_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sources/summary")
async def get_sources_summary(
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Get summary statistics for all sources."""
    try:
        summary = await source_manager.get_sources_summary()
        return summary
    except Exception as e:
        logger.error(f"Error getting sources summary: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sources/bulk/enable")
async def bulk_enable_sources(
    source_ids: List[str],
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Enable multiple sources in bulk."""
    try:
        results = await source_manager.bulk_enable_sources(source_ids)
        return results
    except Exception as e:
        logger.error(f"Error in bulk enable sources: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sources/bulk/disable")
async def bulk_disable_sources(
    source_ids: List[str],
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Disable multiple sources in bulk."""
    try:
        results = await source_manager.bulk_disable_sources(source_ids)
        return results
    except Exception as e:
        logger.error(f"Error in bulk disable sources: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


async def _validate_pipeline_sources(
    source_manager: AdminSourceManager,
    source_ids: Optional[List[str]],
    pipeline_mode: PipelineMode
) -> None:
    """
    Validate that sources are available for pipeline execution.

    Args:
        source_manager: The source manager instance
        source_ids: Optional list of specific source IDs to process
        pipeline_mode: The pipeline execution mode

    Raises:
        HTTPException: If validation fails
    """
    try:
        if source_ids:
            # Validate specific sources
            available_sources = []
            missing_sources = []
            disabled_sources = []

            for source_id in source_ids:
                source = await source_manager.get_source(source_id)
                if not source:
                    missing_sources.append(source_id)
                elif not source.enabled:
                    disabled_sources.append(source.name)
                else:
                    available_sources.append(source)

            if missing_sources:
                raise HTTPException(
                    status_code=404,
                    detail={
                        "error": "Sources not found",
                        "message": f"The following source IDs were not found: {', '.join(missing_sources)}",
                        "missing_sources": missing_sources,
                        "suggestion": "Please check the source IDs and try again."
                    }
                )

            if disabled_sources:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "error": "Disabled sources selected",
                        "message": f"The following sources are disabled: {', '.join(disabled_sources)}",
                        "disabled_sources": disabled_sources,
                        "suggestion": "Please enable these sources or remove them from the selection."
                    }
                )

            if not available_sources:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "error": "No valid sources",
                        "message": "No valid sources available for processing.",
                        "suggestion": "Please add and enable at least one data source before starting the pipeline."
                    }
                )
        else:
            # Validate all enabled sources based on pipeline mode
            if pipeline_mode == PipelineMode.FULL:
                sources = await source_manager.get_all_sources(enabled_only=True)
            elif pipeline_mode == PipelineMode.INCREMENTAL:
                # For incremental, we need sources that might need updates
                sources = await source_manager.get_all_sources(enabled_only=True)
            elif pipeline_mode == PipelineMode.REPROCESS:
                # For reprocess, we need sources that have been processed before
                sources = await source_manager.get_all_sources(enabled_only=True)
            else:  # VALIDATE
                # For validation, we can work with all sources
                sources = await source_manager.get_all_sources(enabled_only=False)

            if not sources:
                total_sources = await source_manager.get_all_sources(enabled_only=False)
                if not total_sources:
                    raise HTTPException(
                        status_code=400,
                        detail={
                            "error": "No data sources configured",
                            "message": "No data sources have been configured in the system.",
                            "suggestion": "Please add at least one data source before starting the pipeline.",
                            "next_steps": [
                                "Go to the Sources page",
                                "Click 'Add Source' to configure a new data source",
                                "Enable the source and try starting the pipeline again"
                            ]
                        }
                    )
                else:
                    raise HTTPException(
                        status_code=400,
                        detail={
                            "error": "No enabled sources",
                            "message": f"Found {len(total_sources)} data sources, but none are enabled.",
                            "suggestion": "Please enable at least one data source before starting the pipeline.",
                            "next_steps": [
                                "Go to the Sources page",
                                "Enable one or more existing sources",
                                "Try starting the pipeline again"
                            ]
                        }
                    )

        logger.info(f"Pipeline source validation passed for mode: {pipeline_mode.value}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating pipeline sources: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Validation error",
                "message": "An error occurred while validating sources.",
                "suggestion": "Please try again or contact support if the problem persists."
            }
        )


@router.post("/pipeline/start")
async def start_pipeline(
    mode: str = "full",
    source_ids: Optional[List[str]] = None,
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Start pipeline execution with source validation."""
    try:
        # Validate mode
        try:
            pipeline_mode = PipelineMode(mode)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid pipeline mode: {mode}")

        # Validate sources before starting pipeline
        await _validate_pipeline_sources(source_manager, source_ids, pipeline_mode)

        result = await pipeline_manager.start_pipeline(
            mode=pipeline_mode,
            source_ids=source_ids
        )

        logger.info(f"Started pipeline: {result['task_id']}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting pipeline: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reindex")
async def trigger_reindex(
    reindex_request: ReindexRequest,
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Trigger reindexing of data sources."""
    try:
        # Validate sources before starting reindexing
        await _validate_pipeline_sources(
            source_manager,
            reindex_request.source_ids,
            PipelineMode.REPROCESS
        )

        result = await pipeline_manager.start_pipeline(
            mode=PipelineMode.REPROCESS,
            source_ids=reindex_request.source_ids
        )

        logger.info(f"Triggered reindex for sources: {reindex_request.source_ids}")
        return {
            "message": "Reindexing started",
            "task_id": result["task_id"],
            "sources": reindex_request.source_ids or "all"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering reindex: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/pipeline/preview")
async def preview_pipeline(
    mode: str = "full",
    source_ids: Optional[List[str]] = None,
    admin_verified: bool = Depends(verify_admin_token),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Preview which sources would be processed by a pipeline run."""
    try:
        # Validate mode
        try:
            pipeline_mode = PipelineMode(mode)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid pipeline mode: {mode}")

        # Get sources that would be processed
        if source_ids:
            # Preview specific sources
            sources_to_process = []
            missing_sources = []
            disabled_sources = []

            for source_id in source_ids:
                source = await source_manager.get_source(source_id)
                if not source:
                    missing_sources.append(source_id)
                elif not source.enabled:
                    disabled_sources.append({
                        "id": source.id,
                        "name": source.name,
                        "status": "disabled"
                    })
                else:
                    sources_to_process.append({
                        "id": source.id,
                        "name": source.name,
                        "source_type": source.source_type,
                        "status": source.status,
                        "last_processed": source.last_processed
                    })

            return {
                "mode": mode,
                "sources_to_process": sources_to_process,
                "disabled_sources": disabled_sources,
                "missing_sources": missing_sources,
                "total_sources": len(sources_to_process),
                "can_execute": len(sources_to_process) > 0 and len(missing_sources) == 0,
                "warnings": _generate_pipeline_warnings(sources_to_process, disabled_sources, missing_sources)
            }
        else:
            # Preview all sources based on mode
            if pipeline_mode == PipelineMode.FULL:
                enabled_sources = await source_manager.get_all_sources(enabled_only=True)
                all_sources = await source_manager.get_all_sources(enabled_only=False)
            elif pipeline_mode == PipelineMode.INCREMENTAL:
                enabled_sources = await source_manager.get_all_sources(enabled_only=True)
                all_sources = await source_manager.get_all_sources(enabled_only=False)
            elif pipeline_mode == PipelineMode.REPROCESS:
                enabled_sources = await source_manager.get_all_sources(enabled_only=True)
                all_sources = await source_manager.get_all_sources(enabled_only=False)
            else:  # VALIDATE
                enabled_sources = await source_manager.get_all_sources(enabled_only=False)
                all_sources = enabled_sources

            sources_to_process = [
                {
                    "id": source.id,
                    "name": source.name,
                    "source_type": source.source_type,
                    "status": source.status,
                    "last_processed": source.last_processed
                }
                for source in enabled_sources
            ]

            disabled_sources = [
                {
                    "id": source.id,
                    "name": source.name,
                    "status": "disabled"
                }
                for source in all_sources if not source.enabled
            ]

            return {
                "mode": mode,
                "sources_to_process": sources_to_process,
                "disabled_sources": disabled_sources,
                "missing_sources": [],
                "total_sources": len(sources_to_process),
                "can_execute": len(sources_to_process) > 0,
                "warnings": _generate_pipeline_warnings(sources_to_process, disabled_sources, [])
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error previewing pipeline: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


def _generate_pipeline_warnings(sources_to_process, disabled_sources, missing_sources):
    """Generate warnings for pipeline preview."""
    warnings = []

    if len(sources_to_process) == 0:
        if len(disabled_sources) > 0:
            warnings.append({
                "type": "no_enabled_sources",
                "message": f"No enabled sources found. {len(disabled_sources)} sources are disabled.",
                "suggestion": "Enable at least one source to run the pipeline."
            })
        else:
            warnings.append({
                "type": "no_sources",
                "message": "No data sources configured.",
                "suggestion": "Add at least one data source before running the pipeline."
            })

    if len(missing_sources) > 0:
        warnings.append({
            "type": "missing_sources",
            "message": f"{len(missing_sources)} selected sources were not found.",
            "suggestion": "Remove missing sources from selection or check source IDs."
        })

    if len(disabled_sources) > 0 and len(sources_to_process) > 0:
        warnings.append({
            "type": "some_disabled",
            "message": f"{len(disabled_sources)} sources are disabled and will be skipped.",
            "suggestion": "Enable disabled sources if you want to include them in processing."
        })

    return warnings


@router.get("/collection-methods")
async def get_collection_methods(admin_verified: bool = Depends(verify_admin_token)):
    """Get available collection methods and their configurations."""
    from src.shared.models import CollectionMethod, CrawlingStrategy, ProcessingPreset

    return {
        "collection_methods": [
            {
                "value": CollectionMethod.STANDARD,
                "label": "Standard Collection",
                "description": "Balanced approach suitable for most websites and documents",
                "recommended_for": ["General websites", "Blog posts", "Documentation"],
                "default_config": {
                    "chunk_size": 1000,
                    "chunk_overlap": 200,
                    "crawling_strategy": CrawlingStrategy.BREADTH_FIRST,
                    "respect_robots_txt": True
                }
            },
            {
                "value": CollectionMethod.DEEP_CRAWL,
                "label": "Deep Crawl",
                "description": "Comprehensive crawling with detailed content extraction",
                "recommended_for": ["Large websites", "Knowledge bases", "Technical documentation"],
                "default_config": {
                    "chunk_size": 1500,
                    "chunk_overlap": 300,
                    "crawling_strategy": CrawlingStrategy.DEPTH_FIRST,
                    "respect_robots_txt": True
                }
            },
            {
                "value": CollectionMethod.CONTENT_ONLY,
                "label": "Content Only",
                "description": "Focus on main content, skip navigation and metadata",
                "recommended_for": ["News articles", "Blog posts", "Clean content extraction"],
                "default_config": {
                    "chunk_size": 800,
                    "chunk_overlap": 150,
                    "crawling_strategy": CrawlingStrategy.BREADTH_FIRST,
                    "respect_robots_txt": True
                }
            },
            {
                "value": CollectionMethod.SELECTIVE,
                "label": "Selective Collection",
                "description": "Custom filtering and selective content extraction",
                "recommended_for": ["Specific content types", "Filtered datasets", "Custom requirements"],
                "default_config": {
                    "chunk_size": 1200,
                    "chunk_overlap": 250,
                    "crawling_strategy": CrawlingStrategy.PRIORITY_BASED,
                    "respect_robots_txt": True
                }
            }
        ],
        "crawling_strategies": [
            {
                "value": CrawlingStrategy.BREADTH_FIRST,
                "label": "Breadth-First",
                "description": "Explore all pages at current depth before going deeper"
            },
            {
                "value": CrawlingStrategy.DEPTH_FIRST,
                "label": "Depth-First",
                "description": "Follow links deeply before exploring other branches"
            },
            {
                "value": CrawlingStrategy.PRIORITY_BASED,
                "label": "Priority-Based",
                "description": "Prioritize important pages based on content relevance"
            }
        ],
        "processing_presets": [
            {
                "value": ProcessingPreset.GENERAL_WEB,
                "label": "General Web Content",
                "description": "Optimized for general web pages and blogs",
                "config": {
                    "chunk_size": 1000,
                    "chunk_overlap": 200,
                    "allowed_content_types": ["text/html", "text/plain"],
                    "max_file_size_mb": 10
                }
            },
            {
                "value": ProcessingPreset.LEGAL_DOCUMENTS,
                "label": "Legal Documents",
                "description": "Specialized for legal texts and contracts",
                "config": {
                    "chunk_size": 1500,
                    "chunk_overlap": 300,
                    "allowed_content_types": ["application/pdf", "text/plain"],
                    "max_file_size_mb": 50
                }
            },
            {
                "value": ProcessingPreset.ACADEMIC_PAPERS,
                "label": "Academic Papers",
                "description": "Optimized for research papers and academic content",
                "config": {
                    "chunk_size": 1200,
                    "chunk_overlap": 250,
                    "allowed_content_types": ["application/pdf", "text/plain"],
                    "max_file_size_mb": 25
                }
            },
            {
                "value": ProcessingPreset.TECHNICAL_DOCS,
                "label": "Technical Documentation",
                "description": "Specialized for API docs and technical guides",
                "config": {
                    "chunk_size": 800,
                    "chunk_overlap": 150,
                    "allowed_content_types": ["text/html", "text/markdown", "text/plain"],
                    "max_file_size_mb": 15
                }
            },
            {
                "value": ProcessingPreset.NEWS_ARTICLES,
                "label": "News Articles",
                "description": "Optimized for news content and articles",
                "config": {
                    "chunk_size": 600,
                    "chunk_overlap": 100,
                    "allowed_content_types": ["text/html", "text/plain"],
                    "max_file_size_mb": 5
                }
            }
        ]
    }


@router.get("/pipeline/tasks/{task_id}/status")
async def get_task_status(
    task_id: str,
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager)
):
    """Get the status of a pipeline task."""
    try:
        status = await pipeline_manager.get_task_status(task_id)
        return status
    except Exception as e:
        logger.error(f"Error getting task status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/pipeline/tasks/{task_id}/cancel")
async def cancel_task(
    task_id: str,
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager)
):
    """Cancel a running pipeline task."""
    try:
        result = await pipeline_manager.cancel_task(task_id)
        return result
    except Exception as e:
        logger.error(f"Error cancelling task: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pipeline/tasks/active")
async def get_active_tasks(
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager)
):
    """Get all currently active pipeline tasks."""
    try:
        tasks = await pipeline_manager.get_active_tasks()
        return {"active_tasks": tasks}
    except Exception as e:
        logger.error(f"Error getting active tasks: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pipeline/tasks/history")
async def get_task_history(
    limit: int = Query(100, ge=1, le=1000),
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager)
):
    """Get pipeline task execution history."""
    try:
        history = await pipeline_manager.get_task_history(limit)
        return {"task_history": history}
    except Exception as e:
        logger.error(f"Error getting task history: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pipeline/statistics")
async def get_pipeline_statistics(
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager)
):
    """Get pipeline execution statistics."""
    try:
        stats = await pipeline_manager.get_pipeline_statistics()
        return stats
    except Exception as e:
        logger.error(f"Error getting pipeline statistics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pipeline/workers")
async def get_worker_status(
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager)
):
    """Get Celery worker status."""
    try:
        status = await pipeline_manager.get_worker_status()
        return status
    except Exception as e:
        logger.error(f"Error getting worker status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Legacy endpoint for backward compatibility
@router.get("/reindex/status/{task_id}")
async def get_reindex_status(
    task_id: str,
    admin_verified: bool = Depends(verify_admin_token),
    pipeline_manager: PipelineManager = Depends(get_pipeline_manager)
):
    """Get the status of a reindexing task (legacy endpoint)."""
    try:
        status = await pipeline_manager.get_task_status(task_id)
        return {
            "task_id": task_id,
            "status": status.get("status", "unknown"),
            "progress": status.get("progress", 0),
            "message": status.get("message", "Task status unknown")
        }
    except Exception as e:
        logger.error(f"Error getting reindex status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error getting reindex status")


# System Monitoring Endpoints
@router.get("/system/status")
async def get_system_status(
    admin_verified: bool = Depends(verify_admin_token),
    system_monitor: SystemMonitor = Depends(get_system_monitor)
):
    """Get comprehensive system status."""
    try:
        status = await system_monitor.get_system_status()
        return system_monitor.get_system_status_dict(status)
    except Exception as e:
        logger.error(f"Error getting system status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/health/{service_name}")
async def get_service_health(
    service_name: str,
    admin_verified: bool = Depends(verify_admin_token),
    system_monitor: SystemMonitor = Depends(get_system_monitor)
):
    """Get health status for a specific service."""
    try:
        health = await system_monitor.check_service_health(service_name)
        return system_monitor.get_service_health_dict(health)
    except Exception as e:
        logger.error(f"Error getting service health: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/metrics")
async def get_system_metrics(
    admin_verified: bool = Depends(verify_admin_token),
    system_monitor: SystemMonitor = Depends(get_system_monitor)
):
    """Get current system performance metrics."""
    try:
        metrics = await system_monitor.get_system_metrics()
        return system_monitor.get_system_metrics_dict(metrics)
    except Exception as e:
        logger.error(f"Error getting system metrics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/metrics/history")
async def get_metrics_history(
    hours: int = Query(24, ge=1, le=168),
    admin_verified: bool = Depends(verify_admin_token),
    system_monitor: SystemMonitor = Depends(get_system_monitor)
):
    """Get system metrics history."""
    try:
        history = await system_monitor.get_metrics_history(hours)
        return {
            "metrics_history": [
                system_monitor.get_system_metrics_dict(m) for m in history
            ]
        }
    except Exception as e:
        logger.error(f"Error getting metrics history: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Analytics Endpoints
@router.get("/analytics/queries")
async def get_query_analytics(
    days: int = Query(7, ge=1, le=90),
    admin_verified: bool = Depends(verify_admin_token),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """Get query analytics for the specified number of days."""
    try:
        analytics = await analytics_service.get_query_analytics(days)
        return analytics
    except Exception as e:
        logger.error(f"Error getting query analytics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/analytics/sessions")
async def get_session_analytics(
    days: int = Query(7, ge=1, le=90),
    admin_verified: bool = Depends(verify_admin_token),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """Get session analytics for the specified number of days."""
    try:
        analytics = await analytics_service.get_session_analytics(days)
        return analytics
    except Exception as e:
        logger.error(f"Error getting session analytics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/analytics/performance")
async def get_performance_analytics(
    days: int = Query(7, ge=1, le=90),
    admin_verified: bool = Depends(verify_admin_token),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """Get performance analytics for the specified number of days."""
    try:
        analytics = await analytics_service.get_performance_analytics(days)
        return analytics
    except Exception as e:
        logger.error(f"Error getting performance analytics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/analytics/summary")
async def get_usage_summary(
    admin_verified: bool = Depends(verify_admin_token),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """Get overall usage summary."""
    try:
        summary = await analytics_service.get_usage_summary()
        return summary
    except Exception as e:
        logger.error(f"Error getting usage summary: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Configuration Management Endpoints
@router.get("/config")
async def get_all_configs(
    admin_verified: bool = Depends(verify_admin_token),
    config_manager: ConfigManager = Depends(get_config_manager)
):
    """Get all configurations."""
    try:
        configs = await config_manager.get_all_configs()
        return configs
    except Exception as e:
        logger.error(f"Error getting configurations: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/{config_name}")
async def get_config(
    config_name: str,
    admin_verified: bool = Depends(verify_admin_token),
    config_manager: ConfigManager = Depends(get_config_manager)
):
    """Get a specific configuration."""
    try:
        config = await config_manager.get_config(config_name)
        return {config_name: config}
    except Exception as e:
        logger.error(f"Error getting configuration {config_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config/{config_name}")
async def update_config(
    config_name: str,
    config_data: Dict[str, Any],
    validate: bool = Query(True),
    admin_verified: bool = Depends(verify_admin_token),
    config_manager: ConfigManager = Depends(get_config_manager)
):
    """Update a configuration."""
    try:
        result = await config_manager.update_config(config_name, config_data, validate)
        return result
    except Exception as e:
        logger.error(f"Error updating configuration {config_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config/{config_name}/validate")
async def validate_config(
    config_name: str,
    config_data: Dict[str, Any],
    admin_verified: bool = Depends(verify_admin_token),
    config_manager: ConfigManager = Depends(get_config_manager)
):
    """Validate a configuration."""
    try:
        result = await config_manager.validate_config(config_name, config_data)
        return result
    except Exception as e:
        logger.error(f"Error validating configuration {config_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config/{config_name}/backup")
async def backup_config(
    config_name: str,
    admin_verified: bool = Depends(verify_admin_token),
    config_manager: ConfigManager = Depends(get_config_manager)
):
    """Create a backup of a configuration."""
    try:
        result = await config_manager.backup_config(config_name)
        return result
    except Exception as e:
        logger.error(f"Error backing up configuration {config_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config/{config_name}/restore")
async def restore_config(
    config_name: str,
    backup_timestamp: str,
    admin_verified: bool = Depends(verify_admin_token),
    config_manager: ConfigManager = Depends(get_config_manager)
):
    """Restore a configuration from backup."""
    try:
        result = await config_manager.restore_config(config_name, backup_timestamp)
        return result
    except Exception as e:
        logger.error(f"Error restoring configuration {config_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/{config_name}/history")
async def get_config_history(
    config_name: str,
    admin_verified: bool = Depends(verify_admin_token),
    config_manager: ConfigManager = Depends(get_config_manager)
):
    """Get configuration change history."""
    try:
        history = await config_manager.get_config_history(config_name)
        return {"config_history": history}
    except Exception as e:
        logger.error(f"Error getting configuration history {config_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Legacy endpoint for backward compatibility
@router.get("/metrics", response_model=MetricsResponse)
async def get_system_metrics_legacy(
    admin_verified: bool = Depends(verify_admin_token),
    system_monitor: SystemMonitor = Depends(get_system_monitor),
    source_manager: AdminSourceManager = Depends(get_source_manager)
):
    """Get system metrics and statistics (legacy endpoint)."""
    try:
        # Get real metrics from services
        system_status = await system_monitor.get_system_status()
        sources_summary = await source_manager.get_sources_summary()

        # Convert to legacy format
        system_health = {}
        for service in system_status.services:
            system_health[service.name] = service.status

        return MetricsResponse(
            total_documents=0,  # TODO: Get from vector store
            total_chunks=0,     # TODO: Get from vector store
            active_sources=sources_summary.get("enabled_sources", 0),
            system_health=system_health
        )
    except Exception as e:
        logger.error(f"Error getting metrics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error getting metrics")


@router.post("/system/restart")
async def restart_system(
    component: Optional[str] = Query(None, description="Specific component to restart (celery, redis, etc.)"),
    admin_verified: bool = Depends(verify_admin_token)
):
    """Restart system components."""
    try:
        logger.warning(f"System restart requested for component: {component or 'all'}")

        if component:
            # Restart specific component
            if component == "celery":
                # TODO: Implement Celery worker restart
                return {"message": f"Celery workers restart initiated"}
            else:
                return {"message": f"Component {component} restart not implemented"}
        else:
            # Full system restart
            return {"message": "Full system restart initiated"}
    except Exception as e:
        logger.error(f"Error restarting system: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error restarting system")


# Health check endpoint for admin interface
@router.get("/health")
async def admin_health_check():
    """Admin interface health check."""
    try:
        return {
            "status": "healthy",
            "timestamp": "2025-07-14T00:00:00Z",
            "admin_interface": "operational"
        }
    except Exception as e:
        logger.error(f"Admin health check failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Admin interface unhealthy")


# Database Management Endpoints

@router.get("/database/stats")
async def get_database_stats(
    admin_verified: bool = Depends(verify_admin_token)
):
    """Get database statistics."""
    try:
        from pymilvus import connections, Collection, utility
        from shared.config import get_settings

        settings = get_settings()

        # Connect to Milvus
        connections.connect(
            alias="default",
            host=settings.milvus_host,
            port=settings.milvus_port
        )

        # Get collection info
        collection_name = settings.milvus_collection_name
        if utility.has_collection(collection_name):
            collection = Collection(collection_name)
            collection.load()

            # Get collection stats
            num_entities = collection.num_entities

            return {
                "totalDocuments": num_entities,
                "totalChunks": num_entities,
                "totalSize": 0,  # Would need to calculate actual size
                "indexSize": 0,  # Would need to calculate index size
                "collections": 1 if utility.has_collection(collection_name) else 0,
                "lastUpdated": "2024-01-01T00:00:00Z"  # Would need to track this
            }
        else:
            return {
                "totalDocuments": 0,
                "totalChunks": 0,
                "totalSize": 0,
                "indexSize": 0,
                "collections": 0,
                "lastUpdated": "2024-01-01T00:00:00Z"
            }

    except Exception as e:
        logger.error(f"Error getting database stats: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/database/collections")
async def get_collections(
    admin_verified: bool = Depends(verify_admin_token)
):
    """Get list of vector collections."""
    try:
        from pymilvus import connections, Collection, utility
        from shared.config import get_settings

        settings = get_settings()

        # Connect to Milvus
        connections.connect(
            alias="default",
            host=settings.milvus_host,
            port=settings.milvus_port
        )

        collections = []
        collection_names = utility.list_collections()

        for name in collection_names:
            try:
                collection = Collection(name)
                collection.load()

                num_entities = collection.num_entities

                collections.append({
                    "name": name,
                    "documents": num_entities,
                    "vectors": num_entities,
                    "dimensions": 768,  # Default dimension, would need to get from schema
                    "size": "Unknown",
                    "status": "healthy",
                    "lastUpdated": "2024-01-01T00:00:00Z"
                })
            except Exception as e:
                logger.warning(f"Error getting stats for collection {name}: {e}")
                collections.append({
                    "name": name,
                    "documents": 0,
                    "vectors": 0,
                    "dimensions": 0,
                    "size": "Unknown",
                    "status": "error",
                    "lastUpdated": "2024-01-01T00:00:00Z"
                })

        return {"collections": collections}

    except Exception as e:
        logger.error(f"Error getting collections: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/database/collections")
async def create_collection(
    request: Dict[str, Any],
    admin_verified: bool = Depends(verify_admin_token)
):
    """Create a new vector collection."""
    try:
        from pymilvus import connections, Collection, CollectionSchema, FieldSchema, DataType, utility
        from shared.config import get_settings

        settings = get_settings()
        collection_name = request.get("name")

        if not collection_name:
            raise HTTPException(status_code=400, detail="Collection name is required")

        # Connect to Milvus
        connections.connect(
            alias="default",
            host=settings.milvus_host,
            port=settings.milvus_port
        )

        # Check if collection already exists
        if utility.has_collection(collection_name):
            raise HTTPException(status_code=400, detail=f"Collection '{collection_name}' already exists")

        # Define schema for new collection
        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=255, is_primary=True),
            FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="source_id", dtype=DataType.VARCHAR, max_length=255),
            FieldSchema(name="source_url", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="metadata", dtype=DataType.VARCHAR, max_length=10000),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=768),
            FieldSchema(name="created_at", dtype=DataType.VARCHAR, max_length=50)
        ]

        schema = CollectionSchema(fields, f"Collection for {collection_name}")

        # Create collection
        collection = Collection(
            name=collection_name,
            schema=schema,
            using='default'
        )

        # Create index for vector field
        index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 1024}
        }
        collection.create_index(field_name="embedding", index_params=index_params)

        logger.info(f"Created collection: {collection_name}")

        return {"message": f"Collection '{collection_name}' created successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating collection: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/database/collections/{collection_name}")
async def delete_collection(
    collection_name: str,
    admin_verified: bool = Depends(verify_admin_token)
):
    """Delete a vector collection."""
    try:
        from pymilvus import connections, utility
        from shared.config import get_settings

        settings = get_settings()

        # Connect to Milvus
        connections.connect(
            alias="default",
            host=settings.milvus_host,
            port=settings.milvus_port
        )

        # Check if collection exists
        if not utility.has_collection(collection_name):
            raise HTTPException(status_code=404, detail=f"Collection '{collection_name}' not found")

        # Drop collection
        utility.drop_collection(collection_name)

        logger.info(f"Deleted collection: {collection_name}")

        return {"message": f"Collection '{collection_name}' deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting collection: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/database/documents")
async def get_documents(
    limit: int = Query(50, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    admin_verified: bool = Depends(verify_admin_token)
):
    """Get documents from the database."""
    try:
        from pymilvus import connections, Collection, utility
        from shared.config import get_settings

        settings = get_settings()
        collection_name = settings.milvus_collection_name

        # Connect to Milvus
        connections.connect(
            alias="default",
            host=settings.milvus_host,
            port=settings.milvus_port
        )

        documents = []

        if utility.has_collection(collection_name):
            collection = Collection(collection_name)
            collection.load()

            # Query documents
            results = collection.query(
                expr="id != ''",
                output_fields=["id", "content", "source_id", "source_url", "created_at"],
                limit=limit,
                offset=offset
            )

            for result in results:
                documents.append({
                    "id": result.get("id", ""),
                    "content": result.get("content", "")[:200] + "..." if len(result.get("content", "")) > 200 else result.get("content", ""),
                    "source": result.get("source_url", result.get("source_id", "Unknown")),
                    "created": result.get("created_at", "Unknown")
                })

        return {"documents": documents}

    except Exception as e:
        logger.error(f"Error getting documents: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/database/documents/{document_id}")
async def delete_document(
    document_id: str,
    admin_verified: bool = Depends(verify_admin_token)
):
    """Delete a document from the database."""
    try:
        from pymilvus import connections, Collection, utility
        from shared.config import get_settings

        settings = get_settings()
        collection_name = settings.milvus_collection_name

        # Connect to Milvus
        connections.connect(
            alias="default",
            host=settings.milvus_host,
            port=settings.milvus_port
        )

        if not utility.has_collection(collection_name):
            raise HTTPException(status_code=404, detail="Collection not found")

        collection = Collection(collection_name)
        collection.load()

        # Delete document
        expr = f'id == "{document_id}"'
        collection.delete(expr)
        collection.flush()

        logger.info(f"Deleted document: {document_id}")

        return {"message": f"Document '{document_id}' deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/database/search")
async def search_documents(
    request: Dict[str, Any],
    admin_verified: bool = Depends(verify_admin_token)
):
    """Search documents in the database."""
    try:
        from pymilvus import connections, Collection, utility
        from shared.config import get_settings

        settings = get_settings()
        collection_name = settings.milvus_collection_name
        query = request.get("query", "")
        limit = request.get("limit", 10)

        if not query:
            raise HTTPException(status_code=400, detail="Query is required")

        # Connect to Milvus
        connections.connect(
            alias="default",
            host=settings.milvus_host,
            port=settings.milvus_port
        )

        results = []
        total_found = 0

        if utility.has_collection(collection_name):
            collection = Collection(collection_name)
            collection.load()

            # Simple text search (would need to implement proper vector search)
            search_results = collection.query(
                expr=f'content like "%{query}%"',
                output_fields=["id", "content", "source_id", "source_url", "created_at"],
                limit=limit
            )

            for result in search_results:
                results.append({
                    "id": result.get("id", ""),
                    "content": result.get("content", ""),
                    "source": result.get("source_url", result.get("source_id", "Unknown")),
                    "score": 1.0,  # Would need actual similarity score
                    "created": result.get("created_at", "Unknown")
                })

            total_found = len(results)

        return {
            "results": results,
            "total_found": total_found,
            "query": query
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error searching documents: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
