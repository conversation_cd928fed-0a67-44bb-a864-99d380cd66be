"""
Admin source management service.

This module provides comprehensive data source management capabilities including:
- CRUD operations for data sources
- Integration with offline pipeline source manager
- Source validation and health checking
- Batch operations and bulk management
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

import sys

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.models import DataSource, ProcessingStatus
from shared.utils import load_yaml_file, save_yaml_file
from offline_pipeline.source_manager import DataSourceManager

logger = get_logger(__name__)


class AdminSourceManager:
    """
    Administrative data source management service.
    
    Provides high-level source management operations for the admin interface,
    building on top of the offline pipeline's DataSourceManager.
    """
    
    def __init__(self):
        """Initialize the admin source manager."""
        self.settings = get_settings()
        self.source_manager = DataSourceManager()
        self._initialized = False
        
        logger.info("Admin source manager initialized")
    
    async def initialize(self):
        """Initialize the source manager."""
        try:
            await self.source_manager.initialize()
            self._initialized = True
            logger.info("Admin source manager initialization completed")
        except Exception as e:
            logger.error(f"Admin source manager initialization failed: {e}", exc_info=True)
            raise
    
    async def list_sources(self) -> List[DataSource]:
        """List all configured data sources."""
        try:
            if not self._initialized:
                await self.initialize()
            
            sources = await self.source_manager.get_all_sources()
            logger.info(f"Retrieved {len(sources)} data sources")
            return sources
        except Exception as e:
            logger.error(f"Error listing sources: {e}", exc_info=True)
            raise
    
    async def get_source(self, source_id: str) -> Optional[DataSource]:
        """Get a specific data source by ID."""
        try:
            if not self._initialized:
                await self.initialize()
            
            source = await self.source_manager.get_source(source_id)
            if source:
                logger.info(f"Retrieved source: {source_id}")
            else:
                logger.warning(f"Source not found: {source_id}")
            return source
        except Exception as e:
            logger.error(f"Error getting source {source_id}: {e}", exc_info=True)
            raise
    
    async def create_source(self, source_data: Dict[str, Any]) -> DataSource:
        """Create a new data source."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Validate source data
            await self._validate_source_data(source_data)
            
            # Create DataSource object
            source = DataSource(
                id=source_data.get("id"),
                name=source_data["name"],
                source_type=source_data["source_type"],
                url=source_data.get("url"),
                path=source_data.get("path"),
                crawl_depth=source_data.get("crawl_depth", 2),
                enabled=source_data.get("enabled", True),
                metadata=source_data.get("metadata", {}),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Add to source manager
            success = await self.source_manager.add_source(source)

            if not success:
                raise RuntimeError("Failed to add source to the source manager")

            logger.info(f"Created new source: {source.name} (ID: {source.id})")
            return source
        except Exception as e:
            logger.error(f"Error creating source: {e}", exc_info=True)
            raise
    
    async def update_source(self, source_id: str, source_data: Dict[str, Any]) -> DataSource:
        """Update an existing data source."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Get existing source
            existing_source = await self.source_manager.get_source(source_id)
            if not existing_source:
                raise ValueError(f"Source not found: {source_id}")
            
            # Validate updated data
            await self._validate_source_data(source_data, is_update=True)
            
            # Update source fields
            updated_source = DataSource(
                id=source_id,
                name=source_data.get("name", existing_source.name),
                source_type=source_data.get("source_type", existing_source.source_type),
                url=source_data.get("url", existing_source.url),
                path=source_data.get("path", existing_source.path),
                crawl_depth=source_data.get("crawl_depth", existing_source.crawl_depth),
                enabled=source_data.get("enabled", existing_source.enabled),
                metadata=source_data.get("metadata", existing_source.metadata or {}),
                created_at=existing_source.created_at,
                updated_at=datetime.now()
            )
            
            # Update in source manager
            success = await self.source_manager.update_source(updated_source)

            if not success:
                raise RuntimeError("Failed to update source in the source manager")

            logger.info(f"Updated source: {source_id}")
            return updated_source
        except Exception as e:
            logger.error(f"Error updating source {source_id}: {e}", exc_info=True)
            raise
    
    async def delete_source(self, source_id: str) -> bool:
        """Delete a data source."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Check if source exists
            existing_source = await self.source_manager.get_source(source_id)
            if not existing_source:
                raise ValueError(f"Source not found: {source_id}")
            
            # Delete from source manager
            success = await self.source_manager.delete_source(source_id)
            
            if success:
                logger.info(f"Deleted source: {source_id}")
            else:
                logger.warning(f"Failed to delete source: {source_id}")
            
            return success
        except Exception as e:
            logger.error(f"Error deleting source {source_id}: {e}", exc_info=True)
            raise
    
    async def validate_source(self, source_id: str) -> Dict[str, Any]:
        """Validate a data source configuration."""
        try:
            if not self._initialized:
                await self.initialize()
            
            source = await self.source_manager.get_source(source_id)
            if not source:
                raise ValueError(f"Source not found: {source_id}")
            
            validation_result = await self.source_manager.validate_source(source)
            
            logger.info(f"Validated source: {source_id}")
            return validation_result
        except Exception as e:
            logger.error(f"Error validating source {source_id}: {e}", exc_info=True)
            raise
    
    async def get_source_status(self, source_id: str) -> Dict[str, Any]:
        """Get processing status for a data source."""
        try:
            if not self._initialized:
                await self.initialize()
            
            status = await self.source_manager.get_source_status(source_id)
            
            logger.info(f"Retrieved status for source: {source_id}")
            return status
        except Exception as e:
            logger.error(f"Error getting source status {source_id}: {e}", exc_info=True)
            raise
    
    async def get_sources_summary(self) -> Dict[str, Any]:
        """Get summary statistics for all sources."""
        try:
            if not self._initialized:
                await self.initialize()
            
            sources = await self.source_manager.get_all_sources()
            
            total_sources = len(sources)
            enabled_sources = len([s for s in sources if s.enabled])
            disabled_sources = total_sources - enabled_sources
            
            # Count by type
            type_counts = {}
            for source in sources:
                source_type = source.source_type
                type_counts[source_type] = type_counts.get(source_type, 0) + 1
            
            summary = {
                "total_sources": total_sources,
                "enabled_sources": enabled_sources,
                "disabled_sources": disabled_sources,
                "sources_by_type": type_counts,
                "last_updated": datetime.now().isoformat()
            }
            
            logger.info("Generated sources summary")
            return summary
        except Exception as e:
            logger.error(f"Error getting sources summary: {e}", exc_info=True)
            raise
    
    async def bulk_enable_sources(self, source_ids: List[str]) -> Dict[str, Any]:
        """Enable multiple sources in bulk."""
        try:
            if not self._initialized:
                await self.initialize()
            
            results = {"success": [], "failed": []}
            
            for source_id in source_ids:
                try:
                    source = await self.source_manager.get_source(source_id)
                    if source:
                        source.enabled = True
                        source.updated_at = datetime.now()
                        await self.source_manager.update_source(source)
                        results["success"].append(source_id)
                    else:
                        results["failed"].append({"id": source_id, "error": "Source not found"})
                except Exception as e:
                    results["failed"].append({"id": source_id, "error": str(e)})
            
            logger.info(f"Bulk enabled sources: {len(results['success'])} success, {len(results['failed'])} failed")
            return results
        except Exception as e:
            logger.error(f"Error in bulk enable sources: {e}", exc_info=True)
            raise
    
    async def bulk_disable_sources(self, source_ids: List[str]) -> Dict[str, Any]:
        """Disable multiple sources in bulk."""
        try:
            if not self._initialized:
                await self.initialize()
            
            results = {"success": [], "failed": []}
            
            for source_id in source_ids:
                try:
                    source = await self.source_manager.get_source(source_id)
                    if source:
                        source.enabled = False
                        source.updated_at = datetime.now()
                        await self.source_manager.update_source(source)
                        results["success"].append(source_id)
                    else:
                        results["failed"].append({"id": source_id, "error": "Source not found"})
                except Exception as e:
                    results["failed"].append({"id": source_id, "error": str(e)})
            
            logger.info(f"Bulk disabled sources: {len(results['success'])} success, {len(results['failed'])} failed")
            return results
        except Exception as e:
            logger.error(f"Error in bulk disable sources: {e}", exc_info=True)
            raise
    
    async def _validate_source_data(self, source_data: Dict[str, Any], is_update: bool = False):
        """Validate source data."""
        required_fields = ["name", "source_type"]
        if not is_update:
            required_fields.extend(["url", "path"])  # At least one should be provided
        
        # Check required fields
        for field in required_fields:
            if field not in source_data and field in ["name", "source_type"]:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate source type
        valid_types = ["website", "pdf", "text"]
        if "source_type" in source_data and source_data["source_type"] not in valid_types:
            raise ValueError(f"Invalid source type. Must be one of: {valid_types}")
        
        # Validate URL or path
        if not is_update:
            if not source_data.get("url") and not source_data.get("path"):
                raise ValueError("Either 'url' or 'path' must be provided")
        
        # Validate crawl depth
        if "crawl_depth" in source_data:
            depth = source_data["crawl_depth"]
            if not isinstance(depth, int) or depth < 1 or depth > 10:
                raise ValueError("Crawl depth must be an integer between 1 and 10")
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            if self.source_manager:
                await self.source_manager.cleanup()
            logger.info("Admin source manager cleanup completed")
        except Exception as e:
            logger.error(f"Error during admin source manager cleanup: {e}", exc_info=True)
